package com.weinuo.quickcommands.ui.configuration

import android.content.Context
import com.weinuo.quickcommands.model.SmartReminderType
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem

/**
 * 智慧提醒数据提供器
 *
 * 按照快捷指令ConfigurationDataProvider的架构模式设计，
 * 统一管理所有智慧提醒功能的配置提供者。
 *
 * 设计原则：
 * - 遵循快捷指令的数据提供器模式
 * - 统一管理所有智慧提醒Provider
 * - 支持可扩展的配置架构
 * - 便于添加新的智慧提醒功能
 *
 * 架构对比：
 * - ConfigurationDataProvider -> SmartReminderDataProvider
 * - AppStateConfigProvider -> ScreenRotationReminderConfigProvider
 * - TaskConfigProvider -> 未来的其他智慧提醒Provider
 */
object SmartReminderDataProvider {

    /**
     * 获取所有智慧提醒配置项列表
     *
     * 类似于ConfigurationDataProvider.getItemsForMode()，
     * 但专门用于智慧提醒功能。
     *
     * @param context 上下文，用于获取字符串资源
     * @return 智慧提醒配置项列表
     */
    fun getAllConfigurationItems(context: Context): List<ConfigurationCardItem<SmartReminderType>> {
        return listOf(
            // 屏幕旋转提醒配置项
            getScreenRotationReminderItem(context),

            // 手电筒提醒配置项
            getFlashlightReminderItem(context),

            // 音乐应用提醒配置项
            getMusicAppReminderItem(context)

            // 后续可以在这里添加更多智慧提醒配置项
            // 例如：
            // getBatteryReminderItem(context)
        )
    }

    /**
     * 根据智慧提醒类型获取对应的配置项
     *
     * @param context 上下文
     * @param reminderType 智慧提醒类型
     * @return 对应的配置项，如果找不到则返回null
     */
    fun getConfigurationItem(
        context: Context,
        reminderType: SmartReminderType
    ): ConfigurationCardItem<SmartReminderType>? {
        return when (reminderType) {
            SmartReminderType.SCREEN_ROTATION_REMINDER -> getScreenRotationReminderItem(context)
            SmartReminderType.FLASHLIGHT_REMINDER -> getFlashlightReminderItem(context)
            SmartReminderType.NEW_APP_REMINDER -> getNewAppReminderItem(context)
            SmartReminderType.MUSIC_APP_REMINDER -> getMusicAppReminderItem(context)
            SmartReminderType.SHOPPING_APP_REMINDER -> getShoppingAppReminderItem(context)
            SmartReminderType.APP_LINK_REMINDER -> getAppLinkReminderItem(context)
            SmartReminderType.SHARE_URL_REMINDER -> getShareUrlReminderItem(context)
            SmartReminderType.ADDRESS_REMINDER -> getAddressReminderItem(context)
            // 后续添加更多类型的处理
        }
    }

    /**
     * 根据智慧提醒类型ID获取对应的配置项
     *
     * @param context 上下文
     * @param reminderTypeId 智慧提醒类型ID
     * @return 对应的配置项，如果找不到则返回null
     */
    fun getConfigurationItemById(
        context: Context,
        reminderTypeId: String
    ): ConfigurationCardItem<SmartReminderType>? {
        val reminderType = SmartReminderType.fromId(reminderTypeId) ?: return null
        return getConfigurationItem(context, reminderType)
    }

    /**
     * 获取屏幕旋转提醒配置项
     *
     * 使用ScreenRotationReminderConfigProvider提供具体配置内容。
     *
     * @param context 上下文
     * @return 屏幕旋转提醒配置项
     */
    private fun getScreenRotationReminderItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return ScreenRotationReminderConfigProvider.getConfigurationItem(context)
    }

    /**
     * 获取手电筒提醒配置项
     *
     * 使用FlashlightReminderConfigProvider提供具体配置内容。
     *
     * @param context 上下文
     * @return 手电筒提醒配置项
     */
    private fun getFlashlightReminderItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return FlashlightReminderConfigProvider.getConfigurationItem(context)
    }

    /**
     * 获取新应用提醒配置项
     *
     * @param context 上下文
     * @return 新应用提醒配置项
     */
    private fun getNewAppReminderItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return NewAppReminderConfigProvider.getConfigurationItem(context)
    }

    /**
     * 获取音乐应用提醒配置项
     *
     * 使用MusicAppReminderConfigProvider提供具体配置内容。
     *
     * @param context 上下文
     * @return 音乐应用提醒配置项
     */
    private fun getMusicAppReminderItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return MusicAppReminderConfigProvider.getConfigurationItem(context)
    }

    /**
     * 获取购物应用提醒配置项
     *
     * 使用ShoppingAppReminderConfigProvider提供具体配置内容。
     *
     * @param context 上下文
     * @return 购物应用提醒配置项
     */
    private fun getShoppingAppReminderItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return ShoppingAppReminderConfigProvider.getConfigurationItem(context)
    }

    /**
     * 获取应用链接提醒配置项
     *
     * 使用AppLinkReminderConfigProvider提供具体配置内容。
     *
     * @param context 上下文
     * @return 应用链接提醒配置项
     */
    private fun getAppLinkReminderItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return AppLinkReminderConfigProvider.getConfigurationItem(context)
    }

    /**
     * 获取分享网址提醒配置项
     *
     * 使用ShareUrlReminderConfigProvider提供具体配置内容。
     *
     * @param context 上下文
     * @return 分享网址提醒配置项
     */
    private fun getShareUrlReminderItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return ShareUrlReminderConfigProvider.getConfigurationItem(context)
    }

    /**
     * 获取地址提醒配置项
     *
     * 使用AddressReminderConfigProvider提供具体配置内容。
     *
     * @param context 上下文
     * @return 地址提醒配置项
     */
    private fun getAddressReminderItem(context: Context): ConfigurationCardItem<SmartReminderType> {
        return AddressReminderConfigProvider.getConfigurationItem(context)
    }

    // 后续可以在这里添加更多智慧提醒功能的获取方法
}
