package com.weinuo.quickcommands.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.model.SmartReminderType
import com.weinuo.quickcommands.ui.configuration.SmartReminderDataProvider
import com.weinuo.quickcommands.ui.configuration.ScreenRotationReminderConfigProvider
import com.weinuo.quickcommands.ui.configuration.FlashlightReminderConfigProvider
import com.weinuo.quickcommands.ui.configuration.MusicAppReminderConfigProvider
import com.weinuo.quickcommands.ui.configuration.AddressReminderConfigProvider
import com.weinuo.quickcommands.ui.configuration.NewAppReminderConfigProvider
import com.weinuo.quickcommands.ui.configuration.ShoppingAppReminderConfigProvider
import com.weinuo.quickcommands.ui.configuration.AppLinkReminderConfigProvider
import com.weinuo.quickcommands.ui.configuration.ShareUrlReminderConfigProvider
import kotlinx.coroutines.launch

/**
 * 智慧提醒详细配置界面
 *
 * 采用Provider架构模式，支持可扩展的智慧提醒功能。
 *
 * 设计特点：
 * - 使用Provider架构，便于扩展
 * - 支持编辑模式和新建模式
 * - 完整的配置验证和保存逻辑
 *
 * @param reminderTypeId 智慧提醒类型ID
 * @param initialConfig 初始配置对象（编辑模式使用）
 * @param onConfigured 配置完成回调
 * @param onNavigateBack 返回回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SmartReminderDetailConfigScreen(
    reminderTypeId: String,
    initialConfig: Any? = null,
    onConfigured: (Any) -> Unit,
    onNavigateBack: () -> Unit,
    navController: NavController? = null
) {
    val context = LocalContext.current

    // 获取智慧提醒类型
    val reminderType = remember(reminderTypeId) {
        SmartReminderType.fromId(reminderTypeId)
    }

    // 如果找不到对应的提醒类型，显示错误界面
    if (reminderType == null) {
        ErrorConfigScreen(
            title = stringResource(R.string.smart_reminder_config_error),
            message = stringResource(R.string.smart_reminder_type_not_found),
            onNavigateBack = onNavigateBack
        )
        return
    }

    // 查找对应的配置项
    val targetConfigItem = remember(reminderType) {
        SmartReminderDataProvider.getConfigurationItem(context, reminderType)
    }

    // 如果找不到对应的配置项，显示错误界面
    if (targetConfigItem == null) {
        ErrorConfigScreen(
            title = stringResource(R.string.smart_reminder_config_error),
            message = stringResource(R.string.smart_reminder_config_not_found),
            onNavigateBack = onNavigateBack
        )
        return
    }

    // 保存函数引用
    var saveFunction by remember { mutableStateOf<(suspend () -> Unit)?>(null) }

    // 使用全屏界面设计
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(reminderType.getLocalizedTitle(context)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                },
                actions = {
                    TextButton(
                        onClick = {
                            // 调用保存函数
                            saveFunction?.let { saveFunc ->
                                // 在协程中执行保存操作
                                kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
                                    saveFunc()
                                    // 保存成功后跳转回智慧提醒界面，只调用onConfigured
                                    onConfigured("config_saved")
                                }
                            }
                        }
                    ) {
                        Text("保存")
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .verticalScroll(rememberScrollState())
        ) {
            // 显示配置内容，传递保存函数设置回调
            when (reminderType) {
                SmartReminderType.SCREEN_ROTATION_REMINDER -> {
                    ScreenRotationReminderConfigProvider.getConfigContent(
                        reminderType = reminderType,
                        onComplete = { result ->
                            // 这里不再自动跳转，由右上角保存按钮控制跳转
                            // onConfigured(result)
                            // onNavigateBack()
                        },
                        onSaveRequested = { saveFunc ->
                            saveFunction = saveFunc
                        }
                    )
                }
                SmartReminderType.FLASHLIGHT_REMINDER -> {
                    FlashlightReminderConfigProvider.getConfigContent(
                        reminderType = reminderType,
                        onComplete = { result ->
                            // 这里不再自动跳转，由右上角保存按钮控制跳转
                            // onConfigured(result)
                            // onNavigateBack()
                        },
                        onSaveRequested = { saveFunc ->
                            saveFunction = saveFunc
                        }
                    )
                }
                SmartReminderType.NEW_APP_REMINDER -> {
                    NewAppReminderConfigProvider.getConfigContent(
                        reminderType = reminderType,
                        onComplete = { result ->
                            // 这里不再自动跳转，由右上角保存按钮控制跳转
                            // onConfigured(result)
                            // onNavigateBack()
                        },
                        onSaveRequested = { saveFunc ->
                            saveFunction = saveFunc
                        }
                    )
                }
                SmartReminderType.MUSIC_APP_REMINDER -> {
                    MusicAppReminderConfigProvider.getConfigContent(
                        reminderType = reminderType,
                        onComplete = { result ->
                            // 这里不再自动跳转，由右上角保存按钮控制跳转
                            // onConfigured(result)
                            // onNavigateBack()
                        },
                        onSaveRequested = { saveFunc ->
                            saveFunction = saveFunc
                        },
                        navController = navController
                    )
                }
                SmartReminderType.SHOPPING_APP_REMINDER -> {
                    // 购物应用提醒配置界面
                    ShoppingAppReminderConfigProvider.getConfigContent(
                        reminderType = reminderType,
                        onComplete = { config ->
                            // 配置完成处理
                        },
                        onSaveRequested = { saveFunc ->
                            saveFunction = saveFunc
                        },
                        navController = navController
                    )
                }
                SmartReminderType.APP_LINK_REMINDER -> {
                    // 应用链接提醒配置界面
                    AppLinkReminderConfigProvider.getConfigContent(
                        reminderType = reminderType,
                        onComplete = { config ->
                            // 配置完成处理
                        },
                        onSaveRequested = { saveFunc ->
                            saveFunction = saveFunc
                        },
                        navController = navController
                    )
                }
                SmartReminderType.SHARE_URL_REMINDER -> {
                    // 分享网址提醒配置界面
                    ShareUrlReminderConfigProvider.getConfigContent(
                        reminderType = reminderType,
                        onComplete = { config ->
                            // 配置完成处理
                        },
                        onSaveRequested = { saveFunc ->
                            saveFunction = saveFunc
                        }
                    )
                }
                SmartReminderType.ADDRESS_REMINDER -> {
                    // 地址提醒配置界面
                    AddressReminderConfigProvider.getConfigContent(
                        reminderType = reminderType,
                        onComplete = { config ->
                            // 配置完成处理
                        },
                        onSaveRequested = { saveFunc ->
                            saveFunction = saveFunc
                        },
                        navController = navController
                    )
                }
            }
        }
    }
}

/**
 * 错误配置界面
 *
 * 当智慧提醒类型或配置项不存在时显示的错误界面。
 *
 * @param title 错误标题
 * @param message 错误消息
 * @param onNavigateBack 返回回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ErrorConfigScreen(
    title: String,
    message: String,
    onNavigateBack: () -> Unit
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(title) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            contentAlignment = androidx.compose.ui.Alignment.Center
        ) {
            Column(
                horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Button(onClick = onNavigateBack) {
                    Text(stringResource(R.string.back))
                }
            }
        }
    }
}
